/**
 * Teste de integração da API com conversão FBX
 * Simula o upload de uma família e testa a conversão
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

async function testApiIntegration() {
    console.log('🧪 Teste de Integração API + Conversão FBX');
    console.log('=' .repeat(50));
    
    const testFilePath = 'C:\\Users\\<USER>\\Downloads\\Furniture_Chairs_Plank_Blocco-Chair.rfa';
    const apiUrl = 'http://localhost:3001/api/families';
    
    try {
        // Verificar se o arquivo existe
        if (!fs.existsSync(testFilePath)) {
            throw new Error(`Arquivo não encontrado: ${testFilePath}`);
        }
        
        console.log('✅ Arquivo encontrado:', testFilePath);
        
        // Criar FormData para simular upload
        const formData = new FormData();
        formData.append('title', 'Teste Cadeira Blocco');
        formData.append('file', fs.createReadStream(testFilePath));
        formData.append('image', fs.createReadStream(testFilePath)); // Usando mesmo arquivo como placeholder
        formData.append('description', 'Teste de conversão FBX');
        formData.append('room', 'Sala de Estar');
        formData.append('category', 'Mobiliário');
        formData.append('manufacturer', 'Teste');
        formData.append('minimal_revit_version', '2024');
        formData.append('price', '100');
        
        console.log('🔄 Enviando para API...');
        
        // Fazer requisição para a API
        const fetch = (await import('node-fetch')).default;
        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData,
            headers: formData.getHeaders()
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Upload bem-sucedido!');
            console.log('📊 Resultado:', result);
            
            // Aguardar um pouco para a conversão
            console.log('⏳ Aguardando conversão FBX...');
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Verificar se o arquivo FBX foi criado
            console.log('🔍 Verificando conversão FBX...');
            
        } else {
            console.log('❌ Erro no upload:', result);
        }
        
    } catch (error) {
        console.error('💥 Erro no teste:', error.message);
    }
    
    console.log('=' .repeat(50));
    console.log('🏁 Teste finalizado');
}

// Executar teste se chamado diretamente
if (require.main === module) {
    testApiIntegration().catch(console.error);
}

module.exports = { testApiIntegration };
