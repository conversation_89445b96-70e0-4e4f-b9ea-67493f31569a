# -*- coding: utf-8 -*-
"""
Conversor direto RFA para FBX usando pyRevit
Executa diretamente no contexto do Revit
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')
clr.AddReference('System')

from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from Autodesk.Revit.ApplicationServices import *
from System.IO import Path as SystemPath

def log_message(message):
    """Log com timestamp"""
    import datetime
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    print("[{0}] {1}".format(timestamp, message))

def main():
    """Função principal - conversão RFA para FBX"""
    try:
        # Parâmetros de entrada
        if len(sys.argv) < 2:
            log_message("❌ Uso: script.py <arquivo_rfa>")
            return
        
        input_file = sys.argv[1]
        output_file = input_file.replace('.rfa', '.fbx').replace('Downloads', 'Desktop')
        
        log_message("🚀 CONVERSÃO REAL RFA → FBX USANDO REVIT")
        log_message("📥 Entrada: {0}".format(input_file))
        log_message("📤 Saída: {0}".format(output_file))
        
        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            log_message("❌ Arquivo não encontrado: {0}".format(input_file))
            return
        
        # Obter aplicação do Revit
        app = __revit__.Application
        log_message("✅ Aplicação Revit obtida")
        
        # Criar novo documento
        log_message("📄 Criando novo documento...")
        doc = app.NewProjectDocument(UnitSystem.Metric)
        log_message("✅ Documento criado")
        
        try:
            # Carregar família
            log_message("📦 Carregando família...")
            with Transaction(doc, "Carregar Família") as trans:
                trans.Start()
                family_loaded = doc.LoadFamily(input_file)
                trans.Commit()
            
            if not family_loaded:
                log_message("❌ Falha ao carregar família")
                return
            
            log_message("✅ Família carregada com sucesso")
            
            # Encontrar símbolo da família
            collector = FilteredElementCollector(doc).OfClass(FamilySymbol)
            family_symbols = list(collector)
            
            if not family_symbols:
                log_message("❌ Nenhum símbolo de família encontrado")
                return
            
            family_symbol = family_symbols[-1]  # Último carregado
            log_message("🎯 Símbolo encontrado: {0}".format(family_symbol.Name))
            
            # Inserir família na origem
            log_message("📍 Inserindo família na origem...")
            with Transaction(doc, "Inserir Família") as trans:
                trans.Start()
                
                if not family_symbol.IsActive:
                    family_symbol.Activate()
                
                origin = XYZ(0, 0, 0)
                family_instance = doc.Create.NewFamilyInstance(
                    origin, 
                    family_symbol, 
                    StructuralType.NonStructural
                )
                
                trans.Commit()
            
            log_message("✅ Família inserida na origem")
            
            # Configurar exportação FBX
            log_message("🔄 Configurando exportação FBX...")
            fbx_options = FBXExportOptions()
            fbx_options.ExportRoomsAsFBX = False
            fbx_options.ExportLinkedFiles = False
            
            # Criar view 3D se necessário
            view_3d = None
            collector = FilteredElementCollector(doc).OfClass(View3D)
            
            for view in collector:
                if not view.IsTemplate:
                    view_3d = view
                    break
            
            if view_3d is None:
                log_message("📐 Criando view 3D...")
                with Transaction(doc, "Criar View 3D") as trans:
                    trans.Start()
                    
                    view_family_type = None
                    collector_vft = FilteredElementCollector(doc).OfClass(ViewFamilyType)
                    
                    for vft in collector_vft:
                        if vft.ViewFamily == ViewFamily.ThreeDimensional:
                            view_family_type = vft
                            break
                    
                    if view_family_type:
                        view_3d = View3D.CreateIsometric(doc, view_family_type.Id)
                        log_message("✅ View 3D criada")
                    
                    trans.Commit()
            
            if view_3d is None:
                log_message("❌ Não foi possível criar view 3D")
                return
            
            # Exportar para FBX
            log_message("📤 Exportando para FBX...")
            view_set = ViewSet()
            view_set.Insert(view_3d)
            
            output_dir = os.path.dirname(output_file)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            result = doc.Export(output_dir, os.path.basename(output_file), view_set, fbx_options)
            
            if result:
                log_message("✅ Exportação FBX concluída com sucesso!")
                log_message("📁 Arquivo criado: {0}".format(output_file))
                
                # Verificar se arquivo foi criado
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    log_message("📏 Tamanho do arquivo: {0} bytes".format(file_size))
                else:
                    log_message("⚠️ Arquivo não encontrado no local esperado")
            else:
                log_message("❌ Falha na exportação FBX")
            
        finally:
            # Fechar documento sem salvar
            doc.Close(False)
            log_message("📄 Documento fechado")
        
        log_message("🎉 CONVERSÃO CONCLUÍDA!")
        
    except Exception as e:
        log_message("❌ Erro: {0}".format(str(e)))
        import traceback
        log_message("📋 Traceback: {0}".format(traceback.format_exc()))

if __name__ == "__main__":
    main()
