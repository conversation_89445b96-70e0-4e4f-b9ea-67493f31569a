/**
 * Script de teste para conversão de família Revit para FBX
 * Testa a implementação usando o arquivo especificado pelo usuário
 */

const fs = require('fs').promises;
const path = require('path');
const { BimRvConverter } = require('./src/lib/bimrv-converter.ts');

async function testConversion() {
    console.log('🧪 Iniciando teste de conversão RFA → FBX');
    console.log('=' .repeat(50));
    
    // Arquivo de teste especificado pelo usuário
    const testFilePath = 'C:\\Users\\<USER>\\Downloads\\Furniture_Chairs_Plank_Blocco-Chair.rfa';
    
    try {
        // Verificar se o arquivo existe
        console.log('📁 Verificando arquivo de teste...');
        await fs.access(testFilePath);
        console.log('✅ Arquivo encontrado:', testFilePath);
        
        // Ler o arquivo
        console.log('📖 Lendo arquivo...');
        const fileBuffer = await fs.readFile(testFilePath);
        console.log(`✅ Arquivo lido: ${fileBuffer.length} bytes`);
        
        // Executar conversão
        console.log('🔄 Iniciando conversão...');
        const startTime = Date.now();
        
        const result = await BimRvConverter.convertAndGetBuffer(
            fileBuffer, 
            path.basename(testFilePath)
        );
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`⏱️ Conversão finalizada em ${duration.toFixed(2)} segundos`);
        
        // Verificar resultado
        if (result.success) {
            console.log('🎉 CONVERSÃO BEM-SUCEDIDA!');
            console.log(`📦 Tamanho do FBX: ${result.objBuffer?.length || 0} bytes`);
            
            // Salvar arquivo FBX para verificação
            if (result.objBuffer) {
                const outputPath = path.join(__dirname, 'test-output.fbx');
                await fs.writeFile(outputPath, result.objBuffer);
                console.log(`💾 Arquivo FBX salvo em: ${outputPath}`);
            }
            
        } else {
            console.log('❌ CONVERSÃO FALHOU!');
            console.log('🔍 Erro:', result.error);
        }
        
    } catch (error) {
        console.error('💥 Erro no teste:', error.message);
        console.error('📋 Stack trace:', error.stack);
    }
    
    console.log('=' .repeat(50));
    console.log('🏁 Teste finalizado');
}

// Executar teste
if (require.main === module) {
    testConversion().catch(console.error);
}

module.exports = { testConversion };
