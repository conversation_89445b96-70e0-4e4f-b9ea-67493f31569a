# -*- coding: utf-8 -*-
"""
Validador de ambiente pyRevit para BIMEX
Verifica se todas as dependências estão disponíveis para conversão FBX
"""

import sys
import os
import datetime

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    print("[{0}] {1}".format(timestamp, message))

def check_python_environment():
    """Verifica o ambiente Python"""
    log_message("🐍 Verificando ambiente Python...")
    
    # Versão do Python
    python_version = "{0}.{1}.{2}".format(sys.version_info.major, sys.version_info.minor, sys.version_info.micro)
    log_message("   - Versão Python: {0}".format(python_version))
    
    # Caminho do executável
    log_message("   - Executável: {0}".format(sys.executable))
    
    # Diretório de trabalho
    log_message("   - Diretório atual: {0}".format(os.getcwd()))
    
    return True

def check_revit_apis():
    """Verifica se as APIs do Revit estão disponíveis"""
    log_message("🏗️ Verificando APIs do Revit...")
    
    try:
        import clr
        log_message("   ✅ CLR disponível")
        
        # Tentar carregar referências do Revit
        clr.AddReference('RevitAPI')
        clr.AddReference('RevitAPIUI')
        clr.AddReference('System')
        log_message("   ✅ Referências do Revit carregadas")
        
        # Tentar importar classes principais
        from Autodesk.Revit.DB import *
        from Autodesk.Revit.UI import *
        from Autodesk.Revit.ApplicationServices import *
        log_message("   ✅ Classes do Revit importadas")
        
        # Verificar se estamos no contexto do Revit
        try:
            app = __revit__.Application
            log_message("   ✅ Contexto do Revit disponível")
            log_message("   - Versão do Revit: {0}".format(app.VersionName))
            return True
        except NameError:
            log_message("   ⚠️ Contexto do Revit não disponível (__revit__ não definido)")
            return False
            
    except ImportError as e:
        log_message("   ❌ Erro ao importar APIs do Revit: {0}".format(e))
        return False
    except Exception as e:
        log_message("   ❌ Erro inesperado: {0}".format(e))
        return False

def check_file_paths():
    """Verifica caminhos de arquivos importantes"""
    log_message("📁 Verificando caminhos de arquivos...")
    
    # Verificar variáveis de ambiente
    env_vars = [
        'REVIT_INSTALL_PATH',
        'PYREVIT_PYTHON_PATH',
        'CONVERTER_TIMEOUT'
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            log_message("   ✅ {0}: {1}".format(var, value))
            
            # Verificar se o caminho existe (para variáveis de caminho)
            if 'PATH' in var and not os.path.exists(value):
                log_message("   ⚠️ Caminho não encontrado: {0}".format(value))
        else:
            log_message("   ❌ {0}: não definido".format(var))
    
    return True

def check_fbx_export_capability():
    """Verifica se a exportação FBX está disponível"""
    log_message("📤 Verificando capacidade de exportação FBX...")
    
    try:
        from Autodesk.Revit.DB import FBXExportOptions
        log_message("   ✅ FBXExportOptions disponível")
        
        # Tentar criar opções FBX
        fbx_options = FBXExportOptions()
        log_message("   ✅ Opções FBX criadas com sucesso")
        
        return True
    except ImportError:
        log_message("   ❌ FBXExportOptions não disponível")
        return False
    except Exception as e:
        log_message("   ❌ Erro ao criar opções FBX: {0}".format(e))
        return False

def test_basic_operations():
    """Testa operações básicas do Revit"""
    log_message("🧪 Testando operações básicas...")
    
    try:
        # Verificar se podemos acessar a aplicação
        app = __revit__.Application
        log_message("   ✅ Aplicação acessível")
        
        # Verificar unidades disponíveis
        from Autodesk.Revit.DB import UnitSystem
        log_message("   ✅ UnitSystem disponível")
        
        # Verificar classes de transação
        from Autodesk.Revit.DB import Transaction
        log_message("   ✅ Transaction disponível")
        
        # Verificar classes de coleção
        from Autodesk.Revit.DB import FilteredElementCollector
        log_message("   ✅ FilteredElementCollector disponível")
        
        return True
    except Exception as e:
        log_message("   ❌ Erro em operações básicas: {0}".format(e))
        return False

def main():
    """Função principal de validação"""
    log_message("🔍 VALIDADOR DE AMBIENTE PYREVIT - BIMEX")
    log_message("=" * 50)
    
    results = []
    
    # Executar verificações
    results.append(("Ambiente Python", check_python_environment()))
    results.append(("APIs do Revit", check_revit_apis()))
    results.append(("Caminhos de arquivos", check_file_paths()))
    results.append(("Exportação FBX", check_fbx_export_capability()))
    results.append(("Operações básicas", test_basic_operations()))
    
    # Resumo dos resultados
    log_message("=" * 50)
    log_message("📊 RESUMO DA VALIDAÇÃO:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSOU" if passed else "❌ FALHOU"
        log_message("   {0}: {1}".format(test_name, status))
        if not passed:
            all_passed = False
    
    log_message("=" * 50)
    
    if all_passed:
        log_message("🎉 AMBIENTE TOTALMENTE VALIDADO!")
        log_message("✅ Pronto para conversão FBX")
        return True
    else:
        log_message("⚠️ AMBIENTE PARCIALMENTE VALIDADO")
        log_message("🔧 Algumas funcionalidades podem não estar disponíveis")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message("💥 ERRO CRÍTICO: {0}".format(e))
        import traceback
        log_message("📋 Traceback: {0}".format(traceback.format_exc()))
        sys.exit(1)
