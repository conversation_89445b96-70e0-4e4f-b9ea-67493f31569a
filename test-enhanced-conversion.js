/**
 * Teste da API reestruturada para conversão FBX
 * Testa a nova estrutura com múltiplas estratégias de conversão
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

async function testEnhancedConversion() {
    console.log('🧪 TESTE DA API REESTRUTURADA - CONVERSÃO FBX');
    console.log('=' .repeat(60));
    
    const testFilePath = 'C:\\Users\\<USER>\\Downloads\\Furniture_Chairs_Plank_Blocco-Chair.rfa';
    const apiUrl = 'http://localhost:3001/api/families';
    
    try {
        // Verificar se o arquivo existe
        if (!fs.existsSync(testFilePath)) {
            throw new Error(`Arquivo não encontrado: ${testFilePath}`);
        }
        
        console.log('✅ Arquivo de teste encontrado:', testFilePath);
        
        // Verificar tamanho do arquivo
        const stats = fs.statSync(testFilePath);
        console.log(`📏 Tamanho do arquivo: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
        
        // Criar FormData para simular upload
        const formData = new FormData();
        formData.append('title', 'Teste Enhanced Converter - Cadeira Blocco');
        formData.append('file', fs.createReadStream(testFilePath));
        
        // Criar imagem placeholder (usando o mesmo arquivo)
        formData.append('image', fs.createReadStream(testFilePath));
        
        // Metadados do produto
        formData.append('description', 'Teste da nova estrutura de conversão FBX com múltiplas estratégias');
        formData.append('room', 'Sala de Estar');
        formData.append('category', 'Mobiliário');
        formData.append('manufacturer', 'BIMEX Test');
        formData.append('minimal_revit_version', '2024');
        formData.append('price', '150');
        formData.append('tags', 'cadeira,mobiliario,teste,fbx');
        
        console.log('🔄 Enviando para API...');
        console.log('📡 URL:', apiUrl);
        
        // Fazer requisição para a API
        const fetch = (await import('node-fetch')).default;
        const startTime = Date.now();
        
        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData,
            headers: formData.getHeaders()
        });
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`⏱️ Tempo de resposta: ${duration.toFixed(2)}s`);
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Upload bem-sucedido!');
            console.log('📊 Resultado da API:');
            console.log('   - ID:', result.id);
            console.log('   - Título:', result.title || 'N/A');
            console.log('   - Arquivo RFA:', result.fileKey || 'N/A');
            console.log('   - Imagem:', result.imageKey || 'N/A');
            
            // Aguardar um pouco para a conversão
            console.log('⏳ Aguardando conversão FBX (10 segundos)...');
            await new Promise(resolve => setTimeout(resolve, 10000));
            
            // Verificar se o arquivo FBX foi criado consultando a API
            console.log('🔍 Verificando resultado da conversão...');
            
            const checkResponse = await fetch(`${apiUrl}?id=${result.id}`);
            if (checkResponse.ok) {
                const families = await checkResponse.json();
                const family = families.find(f => f.id === result.id);
                
                if (family && family.fbx_path) {
                    console.log('✅ Arquivo FBX criado com sucesso!');
                    console.log('📁 Caminho FBX:', family.fbx_path);
                    console.log('🌐 URL FBX:', family.fbxUrl || 'N/A');
                } else {
                    console.log('⚠️ Arquivo FBX não encontrado na resposta da API');
                    console.log('📋 Dados da família:', JSON.stringify(family, null, 2));
                }
            } else {
                console.log('❌ Erro ao verificar conversão:', checkResponse.statusText);
            }
            
        } else {
            console.log('❌ Erro no upload:');
            console.log('   - Status:', response.status);
            console.log('   - Mensagem:', result.error || result.message || 'Erro desconhecido');
            
            if (result.details) {
                console.log('   - Detalhes:', result.details);
            }
        }
        
    } catch (error) {
        console.error('💥 Erro no teste:', error.message);
        
        if (error.code) {
            console.error('   - Código:', error.code);
        }
        
        if (error.stack) {
            console.error('   - Stack:', error.stack.split('\n').slice(0, 3).join('\n'));
        }
    }
    
    console.log('=' .repeat(60));
    console.log('🏁 Teste finalizado');
}

async function testEnvironmentValidation() {
    console.log('🔧 TESTE DE VALIDAÇÃO DO AMBIENTE');
    console.log('-' .repeat(40));
    
    // Verificar variáveis de ambiente
    const requiredEnvVars = [
        'PYREVIT_PYTHON_PATH',
        'REVIT_INSTALL_PATH',
        'CONVERTER_TIMEOUT'
    ];
    
    for (const envVar of requiredEnvVars) {
        const value = process.env[envVar];
        if (value) {
            console.log(`✅ ${envVar}: ${value}`);
            
            // Verificar se o caminho existe (para caminhos de arquivo)
            if (envVar.includes('PATH') && !fs.existsSync(value)) {
                console.log(`   ⚠️ Caminho não encontrado: ${value}`);
            }
        } else {
            console.log(`❌ ${envVar}: não definido`);
        }
    }
    
    console.log('-' .repeat(40));
}

async function main() {
    console.log('🚀 INICIANDO TESTES DA API REESTRUTURADA');
    console.log('');
    
    // Teste 1: Validação do ambiente
    await testEnvironmentValidation();
    console.log('');
    
    // Teste 2: Conversão completa
    await testEnhancedConversion();
}

// Executar testes
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    testEnhancedConversion,
    testEnvironmentValidation
};
