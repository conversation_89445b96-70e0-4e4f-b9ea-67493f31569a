import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

export interface ConversionResult {
  success: boolean;
  fbxPath?: string;
  objPath?: string; // Manter para compatibilidade
  error?: string;
}

export class BimRvConverter {
  private static readonly TEMP_DIR = path.join(process.cwd(), 'temp');
  private static readonly PYREVIT_SCRIPTS_DIR = path.join(process.cwd(), 'pyrevit-scripts');

  // Múltiplas estratégias de conversão em ordem de prioridade
  private static readonly CONVERSION_STRATEGIES = [
    {
      name: 'Enhanced FBX Converter',
      scriptPath: path.join(process.cwd(), 'pyrevit-scripts', 'enhanced_fbx_converter.py'),
      pythonPath: process.env.PYREVIT_PYTHON_PATH || '',
      requiresRevit: true
    },
    {
      name: 'PyRevit Direct Script',
      scriptPath: path.join(process.cwd(), 'pyrevit-scripts', 'direct_fbx_converter.py'),
      pythonPath: process.env.PYREVIT_PYTHON_PATH || '',
      requiresRevit: true
    },
    {
      name: 'PyRevit CLI',
      scriptPath: path.join(process.cwd(), 'pyrevit-scripts', 'bimex_converter.py'),
      pythonPath: process.env.PYREVIT_PYTHON_PATH || process.env.PYTHON_PATH || 'python',
      requiresRevit: true
    }
  ];

  /**
   * Valida se o ambiente está configurado para conversão Revit
   */
  private static async validateEnvironment(): Promise<{ valid: boolean; message: string }> {
    try {
      // Verificar se o Revit está instalado
      const revitPath = process.env.REVIT_INSTALL_PATH;
      if (!revitPath || !await this.fileExists(revitPath)) {
        return { valid: false, message: 'Revit não encontrado no caminho especificado' };
      }

      // Verificar se o pyRevit Python está disponível
      const pyrevitPython = process.env.PYREVIT_PYTHON_PATH;
      if (!pyrevitPython || !await this.fileExists(pyrevitPython)) {
        return { valid: false, message: 'Python do pyRevit não encontrado' };
      }

      return { valid: true, message: 'Ambiente validado com sucesso' };
    } catch (error) {
      return { valid: false, message: `Erro na validação: ${error instanceof Error ? error.message : 'Erro desconhecido'}` };
    }
  }

  /**
   * Converte um arquivo RFA do Revit para formato FBX com máxima preservação de geometria
   * @param inputBuffer Buffer do arquivo RFA
   * @param fileName Nome do arquivo original
   * @returns Promise com o resultado da conversão
   */
  static async convertRfaToFbx(inputBuffer: Buffer, fileName: string): Promise<ConversionResult> {
    try {
      console.log('🔄 Iniciando conversão RFA → FBX para:', fileName);

      // Validar ambiente antes de iniciar
      const envValidation = await this.validateEnvironment();
      if (!envValidation.valid) {
        console.warn('⚠️ Ambiente não validado:', envValidation.message);
        console.log('🔄 Continuando com conversão (modo compatibilidade)...');
      } else {
        console.log('✅ Ambiente validado:', envValidation.message);
      }

      // Criar diretório temporário se não existir
      await this.ensureTempDir();

      // Gerar nomes únicos para os arquivos
      const timestamp = Date.now();
      const inputPath = path.join(this.TEMP_DIR, `${timestamp}_${fileName}`);
      const outputPath = path.join(this.TEMP_DIR, `${timestamp}_${path.parse(fileName).name}.fbx`);

      console.log('📁 Salvando arquivo temporário:', inputPath);
      // Salvar arquivo de entrada
      await fs.writeFile(inputPath, inputBuffer);

      // Tentar conversão com múltiplas estratégias
      let lastError = '';
      for (const strategy of this.CONVERSION_STRATEGIES) {
        console.log(`🔧 Tentando conversão com: ${strategy.name}`);

        // Verificar se a estratégia está disponível
        if (!await this.fileExists(strategy.scriptPath)) {
          console.warn(`⚠️ Script não encontrado: ${strategy.scriptPath}`);
          continue;
        }

        if (!strategy.pythonPath || !await this.fileExists(strategy.pythonPath)) {
          console.warn(`⚠️ Python não encontrado: ${strategy.pythonPath}`);
          continue;
        }

        const conversionResult = await this.executeConversionStrategy(inputPath, outputPath, strategy);

        if (conversionResult.success && await this.fileExists(outputPath)) {
          console.log(`✅ Conversão bem-sucedida com ${strategy.name}`);
          console.log('✅ Arquivo FBX gerado com sucesso:', outputPath);
          console.log('🎯 Geometria preservada com máxima fidelidade');

          // Limpar apenas o arquivo de entrada (manter o FBX para retorno)
          await this.cleanup([inputPath]);

          return {
            success: true,
            fbxPath: outputPath,
            objPath: outputPath // Mantendo nome para compatibilidade, mas agora é FBX
          };
        } else {
          console.warn(`⚠️ Falha na conversão com ${strategy.name}:`, conversionResult.error);
          lastError = conversionResult.error || 'Erro desconhecido';
        }
      }

      // Se chegou aqui, todas as estratégias falharam
      console.error('❌ Todas as estratégias de conversão falharam');
      await this.cleanup([inputPath, outputPath]);

      return {
        success: false,
        error: `Falha em todas as estratégias de conversão. Último erro: ${lastError}`
      };
    } catch (error) {
      console.error('❌ Erro na conversão RFA para FBX:', error);
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }
  /**
   * Executa uma estratégia específica de conversão
   */
  private static async executeConversionStrategy(
    inputPath: string,
    outputPath: string,
    strategy: { name: string; scriptPath: string; pythonPath: string; requiresRevit: boolean }
  ): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      // Preparar argumentos baseado no tipo de script
      let args: string[];

      if (strategy.name === 'Enhanced FBX Converter' || strategy.name === 'PyRevit Direct Script') {
        // Scripts diretos - passam apenas o arquivo de entrada
        args = [strategy.scriptPath, inputPath];
      } else {
        // Script com argumentos nomeados
        args = [strategy.scriptPath, '--input', inputPath, '--output', outputPath];
      }

      console.log(`🚀 Executando: ${strategy.pythonPath} ${args.join(' ')}`);

      const childProcess = spawn(strategy.pythonPath, args, {
        env: {
          ...process.env,
          PYTHONPATH: path.dirname(strategy.scriptPath),
          REVIT_INSTALL_PATH: process.env.REVIT_INSTALL_PATH
        }
      });

      let stdout = '';
      let stderr = '';

      childProcess.stdout?.on('data', (data: any) => {
        stdout += data.toString();
        const output = data.toString().trim();
        if (output) {
          console.log(`📤 ${strategy.name}:`, output);
        }
      });

      childProcess.stderr?.on('data', (data: any) => {
        stderr += data.toString();
        const output = data.toString().trim();
        if (output) {
          console.log(`📤 ${strategy.name} stderr:`, output);
        }
      });

      childProcess.on('close', (code: number | null) => {
        console.log(`🏁 ${strategy.name} finalizou com código: ${code}`);

        if (code === 0) {
          resolve({ success: true });
        } else {
          resolve({
            success: false,
            error: `Processo finalizou com código ${code}. Stderr: ${stderr.slice(0, 500)}`
          });
        }
      });

      childProcess.on('error', (error: Error) => {
        console.error(`❌ Erro ao executar ${strategy.name}:`, error);
        resolve({
          success: false,
          error: `Erro ao executar processo: ${error.message}`
        });
      });

      // Timeout configurável para conversões complexas
      const timeout = parseInt(process.env.CONVERTER_TIMEOUT || '600') * 1000;
      const timeoutHandle = setTimeout(() => {
        if (!childProcess.killed) {
          console.warn(`⏰ Timeout na conversão com ${strategy.name} (${timeout/1000}s)`);
          childProcess.kill();
          resolve({
            success: false,
            error: `Timeout na conversão (${timeout/1000}s)`
          });
        }
      }, timeout);

      // Limpar timeout se o processo terminar antes
      childProcess.on('close', () => {
        clearTimeout(timeoutHandle);
      });
    });
  }

  /**
   * Verifica se um arquivo existe
   */
  private static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Garante que o diretório temporário existe
   */
  private static async ensureTempDir(): Promise<void> {
    try {
      await fs.mkdir(this.TEMP_DIR, { recursive: true });
    } catch (error) {
      // Ignorar erro se o diretório já existir
    }
  }

  /**
   * Remove arquivos temporários
   */
  private static async cleanup(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        if (await this.fileExists(filePath)) {
          await fs.unlink(filePath);
        }
      } catch (error) {
        console.warn(`Erro ao remover arquivo temporário ${filePath}:`, error);
      }
    }
  }

  /**
   * Converte arquivo e retorna o buffer do FBX
   */
  static async convertAndGetBuffer(inputBuffer: Buffer, fileName: string): Promise<{ success: boolean; objBuffer?: Buffer; error?: string }> {
    const result = await this.convertRfaToFbx(inputBuffer, fileName);

    if (result.success && result.objPath) {
      try {
        const fbxBuffer = await fs.readFile(result.objPath);
        await this.cleanup([result.objPath]);
        return { success: true, objBuffer: fbxBuffer }; // Mantendo nome para compatibilidade
      } catch (error) {
        return {
          success: false,
          error: `Erro ao ler arquivo FBX convertido: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        };
      }
    }

    return { success: false, error: result.error };
  }
}
