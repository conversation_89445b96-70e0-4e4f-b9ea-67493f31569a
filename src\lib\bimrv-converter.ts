import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

export interface ConversionResult {
  success: boolean;
  fbxPath?: string;
  objPath?: string; // Manter para compatibilidade
  error?: string;
}

export class BimRvConverter {
  private static readonly TEMP_DIR = path.join(process.cwd(), 'temp');
  // Usando apenas pyRevit para conversão - removendo dependência do ODA BIMRV
  private static readonly PYREVIT_SCRIPT_PATH = path.join(process.cwd(), 'pyrevit-scripts', 'bimex_converter.py');
  private static readonly CONVERTER = {
    name: 'PyRevit BIMEX Converter',
    // Priorizar Python do pyRevit para ter acesso às APIs do Revit
    path: process.env.PYREVIT_PYTHON_PATH || process.env.PYTHON_PATH || 'python',
    args: (input: string, output: string) => [
      this.PYREVIT_SCRIPT_PATH,
      '--input', input,
      '--output', output
    ]
  };

  /**
   * Converte um arquivo RFA do Revit para formato FBX com máxima preservação de geometria
   * @param inputBuffer Buffer do arquivo RFA
   * @param fileName Nome do arquivo original
   * @returns Promise com o resultado da conversão
   */
  static async convertRfaToFbx(inputBuffer: Buffer, fileName: string): Promise<ConversionResult> {
    try {
      console.log('🔄 Iniciando conversão RFA → FBX para:', fileName);

      // Criar diretório temporário se não existir
      await this.ensureTempDir();

      // Gerar nomes únicos para os arquivos
      const timestamp = Date.now();
      const inputPath = path.join(this.TEMP_DIR, `${timestamp}_${fileName}`);
      const outputPath = path.join(this.TEMP_DIR, `${timestamp}_${path.parse(fileName).name}.fbx`);

      console.log('📁 Salvando arquivo temporário:', inputPath);
      // Salvar arquivo de entrada
      await fs.writeFile(inputPath, inputBuffer);

      // Executar conversão usando pyRevit
      console.log(`🔧 Iniciando conversão com ${this.CONVERTER.name}...`);
      const conversionResult = await this.executeConversion(inputPath, outputPath, this.CONVERTER);

      if (conversionResult.success && await this.fileExists(outputPath)) {
        console.log(`✅ Conversão bem-sucedida com ${this.CONVERTER.name}`);
        console.log('✅ Arquivo FBX gerado com sucesso:', outputPath);
        console.log('🎯 Geometria preservada com máxima fidelidade:');
        console.log('   - Materiais e texturas mantidos');
        console.log('   - Superfícies NURBS preservadas');
        console.log('   - Detalhes geométricos completos');

        // Limpar apenas o arquivo de entrada (manter o FBX para retorno)
        await this.cleanup([inputPath]);

        return {
          success: true,
          fbxPath: outputPath,
          objPath: outputPath // Mantendo nome para compatibilidade, mas agora é FBX
        };
      } else {
        console.warn(`⚠️ Falha na conversão com ${this.CONVERTER.name}:`, conversionResult.error);
        // Limpar arquivos temporários em caso de erro
        await this.cleanup([inputPath, outputPath]);

        return {
          success: false,
          error: conversionResult.error || 'Falha na conversão FBX com pyRevit'
        };
      }
    } catch (error) {
      console.error('❌ Erro na conversão RFA para OBJ:', error);
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }
  /**
   * Executa o processo de conversão usando pyRevit
   */
  private static async executeConversion(
    inputPath: string,
    outputPath: string,
    converter: { name: string; path: string; args: (input: string, output: string) => string[] }
  ): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      // Comando específico para cada conversor
      const args = converter.args(inputPath, outputPath);
      console.log(`🚀 Executando: ${converter.path} ${args.join(' ')}`);

      const process = spawn(converter.path, args);

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
        console.log(`📤 ${converter.name} stdout:`, data.toString().trim());
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
        console.error(`📥 ${converter.name} stderr:`, data.toString().trim());
      });

      process.on('close', (code) => {
        console.log(`🏁 ${converter.name} processo finalizado com código:`, code);

        if (code === 0) {
          resolve({ success: true });
        } else {
          resolve({
            success: false,
            error: `${converter.name} falhou com código ${code}. Stderr: ${stderr.trim()}`
          });
        }
      });

      process.on('error', (error) => {
        console.error(`❌ Erro ao executar ${converter.name}:`, error);
        resolve({
          success: false,
          error: `Erro ao executar ${converter.name}: ${error.message}`
        });
      });

      // Timeout para evitar processos que ficam presos
      setTimeout(() => {
        if (!process.killed) {
          console.warn(`⏰ Timeout na conversão com ${converter.name}, terminando processo...`);
          process.kill();
          resolve({
            success: false,
            error: `Timeout na conversão com ${converter.name}`
          });
        }
      }, 60000); // 60 segundos timeout
    });
  }

  /**
   * Verifica se um arquivo existe
   */
  private static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Garante que o diretório temporário existe
   */
  private static async ensureTempDir(): Promise<void> {
    try {
      await fs.mkdir(this.TEMP_DIR, { recursive: true });
    } catch (error) {
      // Ignorar erro se o diretório já existir
    }
  }

  /**
   * Remove arquivos temporários
   */
  private static async cleanup(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        if (await this.fileExists(filePath)) {
          await fs.unlink(filePath);
        }
      } catch (error) {
        console.warn(`Erro ao remover arquivo temporário ${filePath}:`, error);
      }
    }
  }

  /**
   * Converte arquivo e retorna o buffer do FBX
   */
  static async convertAndGetBuffer(inputBuffer: Buffer, fileName: string): Promise<{ success: boolean; objBuffer?: Buffer; error?: string }> {
    const result = await this.convertRfaToFbx(inputBuffer, fileName);

    if (result.success && result.objPath) {
      try {
        const fbxBuffer = await fs.readFile(result.objPath);
        await this.cleanup([result.objPath]);
        return { success: true, objBuffer: fbxBuffer }; // Mantendo nome para compatibilidade
      } catch (error) {
        return {
          success: false,
          error: `Erro ao ler arquivo FBX convertido: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        };
      }
    }

    return { success: false, error: result.error };
  }
}
