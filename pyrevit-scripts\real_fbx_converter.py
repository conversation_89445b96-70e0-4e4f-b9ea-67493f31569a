# -*- coding: utf-8 -*-
"""
Conversor REAL RFA para FBX usando pyRevit
Este script deve ser executado diretamente pelo pyRevit CLI
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')
clr.AddReference('System')

from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from Autodesk.Revit.ApplicationServices import *
from System.IO import Path as SystemPath

def main():
    """Conversão real RFA para FBX"""
    try:
        # Arquivo de entrada (hardcoded para teste)
        input_file = r"C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa"
        output_file = r"C:\Users\<USER>\Desktop\REAL_CHAIR_CONVERTED.fbx"
        
        print("🚀 CONVERSÃO REAL RFA → FBX")
        print("📥 Entrada: {}".format(input_file))
        print("📤 Saída: {}".format(output_file))
        
        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            print("❌ Arquivo não encontrado: {}".format(input_file))
            return
        
        # Obter aplicação do Revit
        app = __revit__.Application
        print("✅ Aplicação Revit obtida")
        
        # Criar novo documento de projeto
        print("📄 Criando novo documento de projeto...")
        doc = app.NewProjectDocument(UnitSystem.Metric)
        print("✅ Documento criado")
        
        try:
            # Iniciar transação para carregar família
            print("📦 Carregando família...")
            with Transaction(doc, "Carregar Família") as trans:
                trans.Start()
                family_loaded = doc.LoadFamily(input_file)
                trans.Commit()
            
            if not family_loaded:
                print("❌ Falha ao carregar família")
                return
            
            print("✅ Família carregada com sucesso")
            
            # Encontrar símbolo da família carregada
            collector = FilteredElementCollector(doc).OfClass(FamilySymbol)
            family_symbols = list(collector)
            
            if not family_symbols:
                print("❌ Nenhum símbolo de família encontrado")
                return
            
            # Pegar o último símbolo (recém carregado)
            family_symbol = family_symbols[-1]
            print("🎯 Símbolo encontrado: {}".format(family_symbol.Name))
            
            # Inserir instância da família na origem
            print("📍 Inserindo família na origem...")
            with Transaction(doc, "Inserir Família") as trans:
                trans.Start()
                
                # Ativar símbolo se necessário
                if not family_symbol.IsActive:
                    family_symbol.Activate()
                
                # Inserir na origem
                origin = XYZ(0, 0, 0)
                family_instance = doc.Create.NewFamilyInstance(
                    origin, 
                    family_symbol, 
                    StructuralType.NonStructural
                )
                
                trans.Commit()
            
            print("✅ Família inserida na origem")
            
            # Configurar opções de exportação FBX
            print("🔄 Configurando exportação FBX...")
            fbx_options = FBXExportOptions()
            fbx_options.ExportRoomsAsFBX = False
            fbx_options.ExportLinkedFiles = False
            
            # Encontrar ou criar view 3D
            view_3d = None
            collector = FilteredElementCollector(doc).OfClass(View3D)
            
            for view in collector:
                if not view.IsTemplate:
                    view_3d = view
                    break
            
            if view_3d is None:
                print("📐 Criando view 3D...")
                with Transaction(doc, "Criar View 3D") as trans:
                    trans.Start()
                    
                    # Encontrar tipo de view 3D
                    view_family_type = None
                    collector_vft = FilteredElementCollector(doc).OfClass(ViewFamilyType)
                    
                    for vft in collector_vft:
                        if vft.ViewFamily == ViewFamily.ThreeDimensional:
                            view_family_type = vft
                            break
                    
                    if view_family_type:
                        view_3d = View3D.CreateIsometric(doc, view_family_type.Id)
                        print("✅ View 3D criada")
                    
                    trans.Commit()
            
            if view_3d is None:
                print("❌ Não foi possível criar view 3D")
                return
            
            # Exportar para FBX
            print("📤 Exportando para FBX...")
            view_set = ViewSet()
            view_set.Insert(view_3d)
            
            # Garantir que o diretório de saída existe
            output_dir = os.path.dirname(output_file)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Executar exportação
            result = doc.Export(
                output_dir, 
                os.path.basename(output_file), 
                view_set, 
                fbx_options
            )
            
            if result:
                print("✅ Exportação FBX concluída com sucesso!")
                print("📁 Arquivo criado: {}".format(output_file))
                
                # Verificar se arquivo foi criado e obter tamanho
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    print("📏 Tamanho do arquivo: {} bytes".format(file_size))
                    
                    if file_size > 1000:  # Arquivo real deve ter mais que 1KB
                        print("🎉 CONVERSÃO REAL CONCLUÍDA COM SUCESSO!")
                        print("✨ Arquivo FBX contém geometria real do Revit")
                    else:
                        print("⚠️ Arquivo muito pequeno - pode estar vazio")
                else:
                    print("⚠️ Arquivo não encontrado no local esperado")
                    # Listar arquivos no diretório
                    print("📂 Arquivos no diretório:")
                    for f in os.listdir(output_dir):
                        if f.endswith('.fbx'):
                            full_path = os.path.join(output_dir, f)
                            size = os.path.getsize(full_path)
                            print("  - {}: {} bytes".format(f, size))
            else:
                print("❌ Falha na exportação FBX")
            
        finally:
            # Fechar documento sem salvar
            doc.Close(False)
            print("📄 Documento fechado")
        
        print("🎉 PROCESSO CONCLUÍDO!")
        
    except Exception as e:
        print("❌ Erro: {}".format(str(e)))
        import traceback
        print("📋 Traceback: {}".format(traceback.format_exc()))

if __name__ == "__main__":
    main()
