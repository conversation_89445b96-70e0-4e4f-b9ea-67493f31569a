/**
 * Teste completo do sistema de conversão BIMEX
 * Testa toda a estrutura reestruturada da API
 */

const fs = require('fs');
const path = require('path');

async function testConversionStatus() {
    console.log('🔍 TESTE DO STATUS DE CONVERSÃO');
    console.log('=' .repeat(50));
    
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch('http://localhost:3001/api/conversion-status');
        
        if (response.ok) {
            const result = await response.json();
            
            console.log('✅ Status obtido com sucesso!');
            console.log('📊 Resumo do sistema:');
            console.log(`   - Pronto: ${result.summary.ready ? '✅ SIM' : '❌ NÃO'}`);
            
            if (result.summary.issues.length > 0) {
                console.log('⚠️ Problemas detectados:');
                result.summary.issues.forEach(issue => {
                    console.log(`   - ${issue}`);
                });
            }
            
            console.log('🔧 Detalhes do ambiente:');
            console.log(`   - Python pyRevit: ${result.status.environment.pyrevitPython.available ? '✅' : '❌'}`);
            console.log(`   - Revit: ${result.status.environment.revitInstall.available ? '✅' : '❌'}`);
            console.log(`   - Scripts disponíveis: ${result.status.environment.scripts.available.length}`);
            console.log(`   - Scripts faltando: ${result.status.environment.scripts.missing.length}`);
            
            if (result.status.validation.success) {
                console.log('✅ Validação do ambiente: PASSOU');
            } else {
                console.log('❌ Validação do ambiente: FALHOU');
                console.log(`   - Motivo: ${result.status.validation.message}`);
            }
            
            return result.summary.ready;
        } else {
            console.log('❌ Erro ao obter status:', response.statusText);
            return false;
        }
    } catch (error) {
        console.error('💥 Erro no teste de status:', error.message);
        return false;
    }
}

async function testManualValidation() {
    console.log('🧪 TESTE DE VALIDAÇÃO MANUAL');
    console.log('=' .repeat(50));
    
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch('http://localhost:3001/api/conversion-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: 'validate' })
        });
        
        if (response.ok) {
            const result = await response.json();
            
            console.log('✅ Validação manual executada!');
            console.log(`📊 Resultado: ${result.validation.success ? '✅ SUCESSO' : '❌ FALHA'}`);
            console.log(`📝 Mensagem: ${result.validation.message}`);
            
            if (result.validation.details) {
                console.log('📋 Detalhes:');
                if (result.validation.details.stdout) {
                    console.log('   - Saída:', result.validation.details.stdout.slice(0, 200) + '...');
                }
                if (result.validation.details.stderr) {
                    console.log('   - Erros:', result.validation.details.stderr.slice(0, 200) + '...');
                }
            }
            
            return result.validation.success;
        } else {
            console.log('❌ Erro na validação manual:', response.statusText);
            return false;
        }
    } catch (error) {
        console.error('💥 Erro na validação manual:', error.message);
        return false;
    }
}

async function testFileConversion() {
    console.log('🔄 TESTE DE CONVERSÃO DE ARQUIVO');
    console.log('=' .repeat(50));
    
    const testFilePath = 'C:\\Users\\<USER>\\Downloads\\Furniture_Chairs_Plank_Blocco-Chair.rfa';
    
    try {
        // Verificar se o arquivo existe
        if (!fs.existsSync(testFilePath)) {
            console.log('❌ Arquivo de teste não encontrado:', testFilePath);
            return false;
        }
        
        console.log('✅ Arquivo de teste encontrado');
        
        const FormData = require('form-data');
        const formData = new FormData();
        
        formData.append('title', 'Teste Sistema Completo - Cadeira');
        formData.append('file', fs.createReadStream(testFilePath));
        formData.append('image', fs.createReadStream(testFilePath));
        formData.append('description', 'Teste completo do sistema reestruturado');
        formData.append('room', 'Teste');
        formData.append('category', 'Mobiliário');
        formData.append('manufacturer', 'BIMEX');
        formData.append('minimal_revit_version', '2024');
        formData.append('price', '200');
        
        console.log('📤 Enviando arquivo para conversão...');
        
        const fetch = (await import('node-fetch')).default;
        const startTime = Date.now();
        
        const response = await fetch('http://localhost:3001/api/families', {
            method: 'POST',
            body: formData,
            headers: formData.getHeaders()
        });
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`⏱️ Tempo de upload: ${duration.toFixed(2)}s`);
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Upload realizado com sucesso!');
            console.log(`📋 ID: ${result.id}`);
            
            // Aguardar conversão
            console.log('⏳ Aguardando conversão (15 segundos)...');
            await new Promise(resolve => setTimeout(resolve, 15000));
            
            // Verificar resultado
            const checkResponse = await fetch('http://localhost:3001/api/families');
            if (checkResponse.ok) {
                const families = await checkResponse.json();
                const family = families.find(f => f.id === result.id);
                
                if (family) {
                    console.log('📊 Resultado da conversão:');
                    console.log(`   - Arquivo RFA: ${family.file_path ? '✅' : '❌'}`);
                    console.log(`   - Imagem: ${family.image_path ? '✅' : '❌'}`);
                    console.log(`   - Arquivo FBX: ${family.fbx_path ? '✅' : '❌'}`);
                    
                    if (family.fbx_path) {
                        console.log(`   - URL FBX: ${family.fbxUrl}`);
                        return true;
                    } else {
                        console.log('❌ Conversão FBX não foi realizada');
                        return false;
                    }
                } else {
                    console.log('❌ Família não encontrada após upload');
                    return false;
                }
            } else {
                console.log('❌ Erro ao verificar resultado');
                return false;
            }
        } else {
            console.log('❌ Erro no upload:', result.error || response.statusText);
            return false;
        }
        
    } catch (error) {
        console.error('💥 Erro na conversão:', error.message);
        return false;
    }
}

async function main() {
    console.log('🚀 TESTE COMPLETO DO SISTEMA BIMEX');
    console.log('🔧 Sistema de conversão RFA → FBX reestruturado');
    console.log('');
    
    const results = [];
    
    // Teste 1: Status do sistema
    console.log('1️⃣ Verificando status do sistema...');
    const statusOk = await testConversionStatus();
    results.push(['Status do Sistema', statusOk]);
    console.log('');
    
    // Teste 2: Validação manual
    console.log('2️⃣ Executando validação manual...');
    const validationOk = await testManualValidation();
    results.push(['Validação Manual', validationOk]);
    console.log('');
    
    // Teste 3: Conversão de arquivo (apenas se o sistema estiver OK)
    if (statusOk) {
        console.log('3️⃣ Testando conversão de arquivo...');
        const conversionOk = await testFileConversion();
        results.push(['Conversão de Arquivo', conversionOk]);
    } else {
        console.log('3️⃣ ⏭️ Pulando teste de conversão (sistema não está pronto)');
        results.push(['Conversão de Arquivo', false]);
    }
    
    // Resumo final
    console.log('');
    console.log('📊 RESUMO DOS TESTES');
    console.log('=' .repeat(50));
    
    let allPassed = true;
    results.forEach(([testName, passed]) => {
        const status = passed ? '✅ PASSOU' : '❌ FALHOU';
        console.log(`${testName}: ${status}`);
        if (!passed) allPassed = false;
    });
    
    console.log('=' .repeat(50));
    
    if (allPassed) {
        console.log('🎉 TODOS OS TESTES PASSARAM!');
        console.log('✅ Sistema pronto para produção');
    } else {
        console.log('⚠️ ALGUNS TESTES FALHARAM');
        console.log('🔧 Verifique a configuração do ambiente');
    }
    
    console.log('');
    console.log('🏁 Teste completo finalizado');
}

// Executar se chamado diretamente
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    testConversionStatus,
    testManualValidation,
    testFileConversion
};
