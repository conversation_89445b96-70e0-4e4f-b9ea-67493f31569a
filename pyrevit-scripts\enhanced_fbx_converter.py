# -*- coding: utf-8 -*-
"""
Enhanced FBX Converter para BIMEX
Conversão RFA → FBX com máxima preservação de geometria
Executado diretamente pelo Python do pyRevit
"""

import sys
import os
import datetime

def log_message(message):
    """Log com timestamp"""
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    print("[{0}] {1}".format(timestamp, message))

# Verificar se estamos no ambiente pyRevit
try:
    import clr
    clr.AddReference('RevitAPI')
    clr.AddReference('RevitAPIUI')
    clr.AddReference('System')
    
    from Autodesk.Revit.DB import *
    from Autodesk.Revit.UI import *
    from Autodesk.Revit.ApplicationServices import *
    
    REVIT_AVAILABLE = True
    log_message("✅ APIs do Revit carregadas com sucesso")
except ImportError as e:
    REVIT_AVAILABLE = False
    log_message("❌ APIs do Revit não disponíveis: {0}".format(e))

def create_high_quality_fbx_options():
    """Cria opções de exportação FBX com máxima qualidade"""
    try:
        fbx_options = FBXExportOptions()
        
        # Configurações básicas
        fbx_options.ExportRoomsAsFBX = False
        fbx_options.ExportLinkedFiles = False
        
        log_message("✅ Opções FBX configuradas para máxima qualidade")
        return fbx_options
    except Exception as e:
        log_message("⚠️ Erro ao configurar opções FBX: {0}".format(e))
        return FBXExportOptions()

def ensure_output_directory(output_path):
    """Garante que o diretório de saída existe"""
    try:
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            log_message("📁 Diretório criado: {0}".format(output_dir))
        return True
    except Exception as e:
        log_message("❌ Erro ao criar diretório: {0}".format(e))
        return False

def main():
    """Função principal - conversão RFA para FBX"""
    try:
        # Verificar disponibilidade do Revit
        if not REVIT_AVAILABLE:
            log_message("❌ APIs do Revit não estão disponíveis")
            return False
        
        # Parâmetros de entrada
        if len(sys.argv) < 2:
            log_message("❌ Uso: script.py <arquivo_rfa>")
            return False
        
        input_file = sys.argv[1]
        
        # Gerar nome de saída mais inteligente
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        input_dir = os.path.dirname(input_file)
        
        # Tentar usar diretório temp primeiro, depois Desktop como fallback
        temp_dir = os.path.join(os.getcwd(), 'temp')
        if not os.path.exists(temp_dir):
            temp_dir = input_dir.replace('Downloads', 'Desktop')
        
        output_file = os.path.join(temp_dir, "{0}_CONVERTED.fbx".format(base_name))
        
        log_message("🚀 ENHANCED FBX CONVERTER - BIMEX")
        log_message("📥 Entrada: {0}".format(input_file))
        log_message("📤 Saída: {0}".format(output_file))
        
        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            log_message("❌ Arquivo não encontrado: {0}".format(input_file))
            return False
        
        # Garantir diretório de saída
        if not ensure_output_directory(output_file):
            return False
        
        # Obter aplicação do Revit
        app = __revit__.Application
        log_message("✅ Aplicação Revit obtida")
        
        # Criar novo documento de projeto
        log_message("📄 Criando novo documento de projeto...")
        doc = app.NewProjectDocument(UnitSystem.Metric)
        log_message("✅ Documento criado")
        
        try:
            # Carregar família
            log_message("📦 Carregando família...")
            with Transaction(doc, "Carregar Família") as trans:
                trans.Start()
                family_loaded = doc.LoadFamily(input_file)
                trans.Commit()
            
            if not family_loaded:
                log_message("❌ Falha ao carregar família")
                return False
            
            log_message("✅ Família carregada com sucesso")
            
            # Encontrar símbolo da família carregada
            collector = FilteredElementCollector(doc).OfClass(FamilySymbol)
            family_symbols = list(collector)
            
            if not family_symbols:
                log_message("❌ Nenhum símbolo de família encontrado")
                return False
            
            # Usar o último símbolo carregado (mais recente)
            family_symbol = family_symbols[-1]
            log_message("🎯 Símbolo encontrado: {0}".format(family_symbol.Name))
            
            # Inserir família na origem
            log_message("📍 Inserindo família na origem...")
            with Transaction(doc, "Inserir Família") as trans:
                trans.Start()
                
                # Ativar símbolo se necessário
                if not family_symbol.IsActive:
                    family_symbol.Activate()
                
                # Inserir na origem
                origin = XYZ(0, 0, 0)
                family_instance = doc.Create.NewFamilyInstance(
                    origin,
                    family_symbol,
                    StructuralType.NonStructural
                )
                
                trans.Commit()
            
            log_message("✅ Família inserida na origem")
            
            # Criar ou encontrar view 3D
            log_message("🔍 Configurando view 3D...")
            view_3d = None
            
            # Procurar view 3D existente
            collector = FilteredElementCollector(doc).OfClass(View3D)
            for view in collector:
                if not view.IsTemplate:
                    view_3d = view
                    break
            
            # Criar view 3D se necessário
            if view_3d is None:
                with Transaction(doc, "Criar View 3D") as trans:
                    trans.Start()
                    
                    # Encontrar tipo de view 3D
                    view_family_type = None
                    collector_vft = FilteredElementCollector(doc).OfClass(ViewFamilyType)
                    for vft in collector_vft:
                        if vft.ViewFamily == ViewFamily.ThreeDimensional:
                            view_family_type = vft
                            break
                    
                    if view_family_type:
                        view_3d = View3D.CreateIsometric(doc, view_family_type.Id)
                        log_message("✅ View 3D criada")
                    
                    trans.Commit()
            
            if view_3d is None:
                log_message("❌ Não foi possível criar view 3D")
                return False
            
            # Configurar exportação FBX
            log_message("🔄 Configurando exportação FBX...")
            fbx_options = create_high_quality_fbx_options()
            
            # Criar ViewSet para exportação
            view_set = ViewSet()
            view_set.Insert(view_3d)
            
            # Executar exportação
            log_message("📤 Exportando para FBX...")
            output_dir = os.path.dirname(output_file)
            output_name = os.path.basename(output_file)
            
            result = doc.Export(output_dir, output_name, view_set, fbx_options)
            
            if result:
                log_message("✅ Exportação FBX concluída com sucesso!")
                log_message("📁 Arquivo criado: {0}".format(output_file))
                
                # Verificar se arquivo foi criado
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    log_message("📏 Tamanho do arquivo: {0} bytes".format(file_size))
                    log_message("🎯 Geometria preservada com máxima fidelidade")
                    return True
                else:
                    log_message("⚠️ Arquivo não encontrado no local esperado")
                    return False
            else:
                log_message("❌ Falha na exportação FBX")
                return False
            
        finally:
            # Fechar documento sem salvar
            doc.Close(False)
            log_message("📄 Documento fechado")
        
    except Exception as e:
        log_message("❌ Erro na conversão: {0}".format(e))
        import traceback
        log_message("📋 Traceback: {0}".format(traceback.format_exc()))
        return False

if __name__ == "__main__":
    success = main()
    if success:
        log_message("🎉 CONVERSÃO CONCLUÍDA COM SUCESSO!")
        sys.exit(0)
    else:
        log_message("💥 CONVERSÃO FALHOU!")
        sys.exit(1)
