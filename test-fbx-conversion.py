#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Teste direto da conversão FBX usando o script bimex_converter.py
"""

import os
import sys
import subprocess
import json
import time

def test_fbx_conversion():
    """Testa a conversão FBX diretamente"""
    print("🧪 TESTE DE CONVERSÃO FBX - BIMEX")
    print("=" * 50)
    
    # Arquivo de teste
    input_file = r"C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa"
    output_file = "test_chair_converted.fbx"
    
    # Verificar se o arquivo existe
    if not os.path.exists(input_file):
        print(f"❌ Arquivo não encontrado: {input_file}")
        return False
    
    print(f"✅ Arquivo encontrado: {input_file}")
    print(f"📤 Arquivo de saída: {output_file}")
    
    # Executar conversão
    print("🔄 Iniciando conversão...")
    start_time = time.time()
    
    try:
        # Executar o script de conversão
        cmd = [
            sys.executable,
            "pyrevit-scripts/bimex_converter.py",
            "--input", input_file,
            "--output", output_file
        ]
        
        print(f"🚀 Comando: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ Tempo de execução: {duration:.2f} segundos")
        print(f"📊 Código de retorno: {result.returncode}")
        
        if result.stdout:
            print("📝 STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ STDERR:")
            print(result.stderr)
        
        # Verificar se o arquivo foi criado
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ Arquivo FBX criado: {output_file}")
            print(f"📏 Tamanho: {file_size} bytes")
            
            # Ler primeiras linhas para verificar conteúdo
            with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(200)
                print(f"📄 Conteúdo (primeiros 200 chars):")
                print(content)
            
            return True
        else:
            print(f"❌ Arquivo FBX não foi criado: {output_file}")
            return False
            
    except Exception as e:
        print(f"💥 Erro durante execução: {e}")
        return False

def main():
    """Função principal"""
    print("🎯 TESTE DIRETO DE CONVERSÃO FBX")
    print("Testando a implementação do BIMEX converter")
    print()
    
    success = test_fbx_conversion()
    
    print()
    print("=" * 50)
    if success:
        print("✅ TESTE BEM-SUCEDIDO!")
        print("🎉 Conversão FBX funcionando")
    else:
        print("❌ TESTE FALHOU!")
        print("🔧 Verifique a configuração do pyRevit")
    print("=" * 50)

if __name__ == "__main__":
    main()
