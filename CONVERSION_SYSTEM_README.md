# Sistema de Conversão BIMEX - RFA para FBX

## Visão Geral

O sistema de conversão BIMEX foi completamente reestruturado para fornecer conversão robusta e confiável de arquivos Revit Family (.rfa) para formato FBX com máxima preservação de geometria.

## Arquitetura do Sistema

### 1. Múltiplas Estratégias de Conversão

O sistema implementa múltiplas estratégias de conversão em ordem de prioridade:

1. **Enhanced FBX Converter** - Script otimizado com máxima preservação de geometria
2. **PyRevit Direct Script** - Script direto usando APIs do Revit
3. **PyRevit CLI** - Fallback usando linha de comando do pyRevit

### 2. Validação de Ambiente

Antes de cada conversão, o sistema valida:
- Disponibilidade do Python do pyRevit
- Instalação do Revit
- Scripts de conversão disponíveis
- APIs do Revit acessíveis

### 3. Tratamento de Erros Robusto

- Timeout configurável para conversões complexas
- Logs detalhados de cada etapa
- Fallback automático entre estratégias
- Limpeza automática de arquivos temporários

## Configuração do Ambiente

### Variáveis de Ambiente Necessárias

```env
# Python do pyRevit
PYREVIT_PYTHON_PATH="C:\Users\<USER>\AppData\Roaming\pyRevit-Master\bin\cengines\CPY3123\python.exe"

# Instalação do Revit
REVIT_INSTALL_PATH="C:\Program Files\Autodesk\Revit 2024"

# Timeout para conversão (em segundos)
CONVERTER_TIMEOUT=600

# Logs verbosos
CONVERTER_VERBOSE_LOGS=true
```

### Estrutura de Arquivos

```
pyrevit-scripts/
├── enhanced_fbx_converter.py    # Script principal otimizado
├── direct_fbx_converter.py      # Script direto
├── bimex_converter.py          # Script CLI
└── validate_environment.py     # Validador de ambiente
```

## Scripts de Conversão

### Enhanced FBX Converter

Script principal com as seguintes características:
- Máxima preservação de geometria
- Configurações FBX otimizadas
- Tratamento de erros robusto
- Logs detalhados de progresso

### Fluxo de Conversão

1. **Validação**: Verificar ambiente e dependências
2. **Preparação**: Criar documento novo do Revit
3. **Carregamento**: Carregar família RFA
4. **Inserção**: Inserir família na origem
5. **Configuração**: Configurar view 3D e opções FBX
6. **Exportação**: Exportar para FBX com máxima qualidade
7. **Limpeza**: Fechar documento e limpar temporários

## API Endpoints

### POST /api/families
Upload e conversão de famílias Revit

### GET /api/conversion-status
Verificar status do sistema de conversão

### POST /api/conversion-status
Executar validação manual do ambiente

## Testes do Sistema

### Scripts de Teste Disponíveis

1. **test-enhanced-conversion.js** - Teste da conversão melhorada
2. **test-complete-system.js** - Teste completo do sistema
3. **validate_environment.py** - Validação do ambiente pyRevit

### Executar Testes

```bash
# Teste completo do sistema
node test-complete-system.js

# Teste específico de conversão
node test-enhanced-conversion.js

# Validação do ambiente
"C:\Users\<USER>\AppData\Roaming\pyRevit-Master\bin\cengines\CPY3123\python.exe" pyrevit-scripts/validate_environment.py
```

## Monitoramento e Diagnóstico

### Verificar Status do Sistema

```bash
curl http://localhost:3001/api/conversion-status
```

### Executar Validação Manual

```bash
curl -X POST http://localhost:3001/api/conversion-status \
  -H "Content-Type: application/json" \
  -d '{"action": "validate"}'
```

## Características Técnicas

### Preservação de Geometria

- **Sem decimação de mesh**: Mantém todos os vértices originais
- **Preservação de materiais**: Mantém texturas e propriedades
- **Superfícies NURBS**: Preserva curvas e superfícies complexas
- **Detalhes geométricos**: Mantém todos os detalhes finos

### Performance

- **Timeout configurável**: Até 10 minutos para arquivos complexos
- **Processamento assíncrono**: Não bloqueia a API
- **Limpeza automática**: Remove arquivos temporários
- **Logs otimizados**: Informações detalhadas sem spam

### Confiabilidade

- **Múltiplas estratégias**: Fallback automático
- **Validação prévia**: Verifica ambiente antes da conversão
- **Tratamento de erros**: Captura e reporta erros detalhados
- **Recuperação automática**: Tenta diferentes abordagens

## Solução de Problemas

### Problemas Comuns

1. **Python do pyRevit não encontrado**
   - Verificar PYREVIT_PYTHON_PATH
   - Confirmar instalação do pyRevit

2. **APIs do Revit não disponíveis**
   - Verificar se o Revit está instalado
   - Confirmar versão compatível

3. **Timeout na conversão**
   - Aumentar CONVERTER_TIMEOUT
   - Verificar complexidade do arquivo

4. **Arquivo FBX não criado**
   - Verificar logs de conversão
   - Testar com arquivo mais simples

### Logs de Debug

Os logs incluem:
- Timestamp de cada operação
- Status de cada etapa
- Erros detalhados com stack trace
- Informações de performance

## Próximos Passos

1. **Otimização**: Melhorar performance para arquivos grandes
2. **Formatos**: Adicionar suporte a outros formatos (OBJ, GLTF)
3. **Batch**: Processamento em lote de múltiplos arquivos
4. **Cache**: Sistema de cache para conversões repetidas
5. **Métricas**: Coleta de métricas de performance e uso

## Suporte

Para problemas ou dúvidas:
1. Verificar logs da aplicação
2. Executar testes de diagnóstico
3. Consultar este README
4. Verificar configuração do ambiente
