import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs/promises';

/**
 * Endpoint de diagnóstico para verificar o status do sistema de conversão
 * GET /api/conversion-status
 */

interface ConversionStatus {
  environment: {
    pyrevitPython: {
      available: boolean;
      path?: string;
      error?: string;
    };
    revitInstall: {
      available: boolean;
      path?: string;
      error?: string;
    };
    scripts: {
      available: string[];
      missing: string[];
    };
  };
  validation: {
    success: boolean;
    message: string;
    details?: any;
  };
  lastCheck: string;
}

async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function validatePyRevitEnvironment(): Promise<{ success: boolean; message: string; details?: any }> {
  return new Promise((resolve) => {
    const validatorScript = path.join(process.cwd(), 'pyrevit-scripts', 'validate_environment.py');
    const pythonPath = process.env.PYREVIT_PYTHON_PATH;

    if (!pythonPath) {
      resolve({
        success: false,
        message: 'PYREVIT_PYTHON_PATH não configurado'
      });
      return;
    }

    const childProcess = spawn(pythonPath, [validatorScript], {
      env: {
        ...process.env,
        PYTHONPATH: path.dirname(validatorScript)
      }
    });

    let stdout = '';
    let stderr = '';

    childProcess.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    childProcess.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    childProcess.on('close', (code) => {
      resolve({
        success: code === 0,
        message: code === 0 ? 'Ambiente validado com sucesso' : 'Falha na validação do ambiente',
        details: {
          exitCode: code,
          stdout: stdout.trim(),
          stderr: stderr.trim()
        }
      });
    });

    childProcess.on('error', (error) => {
      resolve({
        success: false,
        message: `Erro ao executar validação: ${error.message}`,
        details: { error: error.message }
      });
    });

    // Timeout de 30 segundos
    setTimeout(() => {
      if (!childProcess.killed) {
        childProcess.kill();
        resolve({
          success: false,
          message: 'Timeout na validação do ambiente',
          details: { timeout: true }
        });
      }
    }, 30000);
  });
}

export async function GET(req: NextRequest) {
  try {
    console.log('🔍 Verificando status do sistema de conversão...');

    const status: ConversionStatus = {
      environment: {
        pyrevitPython: {
          available: false
        },
        revitInstall: {
          available: false
        },
        scripts: {
          available: [],
          missing: []
        }
      },
      validation: {
        success: false,
        message: 'Não validado'
      },
      lastCheck: new Date().toISOString()
    };

    // Verificar Python do pyRevit
    const pyrevitPython = process.env.PYREVIT_PYTHON_PATH;
    if (pyrevitPython) {
      const exists = await checkFileExists(pyrevitPython);
      status.environment.pyrevitPython = {
        available: exists,
        path: pyrevitPython,
        error: exists ? undefined : 'Arquivo não encontrado'
      };
    } else {
      status.environment.pyrevitPython = {
        available: false,
        error: 'PYREVIT_PYTHON_PATH não configurado'
      };
    }

    // Verificar instalação do Revit
    const revitPath = process.env.REVIT_INSTALL_PATH;
    if (revitPath) {
      const exists = await checkFileExists(revitPath);
      status.environment.revitInstall = {
        available: exists,
        path: revitPath,
        error: exists ? undefined : 'Diretório não encontrado'
      };
    } else {
      status.environment.revitInstall = {
        available: false,
        error: 'REVIT_INSTALL_PATH não configurado'
      };
    }

    // Verificar scripts de conversão
    const scriptsToCheck = [
      'enhanced_fbx_converter.py',
      'direct_fbx_converter.py',
      'bimex_converter.py',
      'validate_environment.py'
    ];

    for (const scriptName of scriptsToCheck) {
      const scriptPath = path.join(process.cwd(), 'pyrevit-scripts', scriptName);
      const exists = await checkFileExists(scriptPath);
      
      if (exists) {
        status.environment.scripts.available.push(scriptName);
      } else {
        status.environment.scripts.missing.push(scriptName);
      }
    }

    // Executar validação completa se o ambiente básico estiver disponível
    if (status.environment.pyrevitPython.available && status.environment.scripts.available.includes('validate_environment.py')) {
      console.log('🧪 Executando validação completa do ambiente...');
      status.validation = await validatePyRevitEnvironment();
    } else {
      status.validation = {
        success: false,
        message: 'Pré-requisitos não atendidos para validação completa'
      };
    }

    // Determinar status geral
    const overallStatus = status.environment.pyrevitPython.available && 
                         status.environment.revitInstall.available && 
                         status.environment.scripts.missing.length === 0;

    console.log(`📊 Status geral: ${overallStatus ? '✅ OK' : '⚠️ Problemas detectados'}`);

    return NextResponse.json({
      success: true,
      status,
      summary: {
        ready: overallStatus,
        issues: [
          ...(!status.environment.pyrevitPython.available ? ['Python do pyRevit não disponível'] : []),
          ...(!status.environment.revitInstall.available ? ['Revit não encontrado'] : []),
          ...(status.environment.scripts.missing.length > 0 ? [`Scripts faltando: ${status.environment.scripts.missing.join(', ')}`] : []),
          ...(!status.validation.success ? ['Validação do ambiente falhou'] : [])
        ]
      }
    });

  } catch (error) {
    console.error('❌ Erro ao verificar status de conversão:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno ao verificar status',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { action } = await req.json();

    if (action === 'validate') {
      console.log('🔄 Executando validação manual do ambiente...');
      
      const validation = await validatePyRevitEnvironment();
      
      return NextResponse.json({
        success: true,
        validation,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Ação não reconhecida'
    }, { status: 400 });

  } catch (error) {
    console.error('❌ Erro na ação de conversão:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}
