# -*- coding: utf-8 -*-
"""
Script IronPython para conversão RFA -> FBX
Executado via pyRevit CLI
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')
clr.AddReference('System')

from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from Autodesk.Revit.ApplicationServices import *
from System.IO import Path as SystemPath

def log_message(message):
    """Log com timestamp"""
    import datetime
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    """Função principal do script IronPython"""
    try:
        input_file = r"C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa"
        output_file = r"C:\Users\<USER>\Desktop\CHAIR_CONVERTED_REAL.fbx"

        log_message(f"🔄 Iniciando conversão: {input_file} -> {output_file}")

        # Obter aplicação do Revit
        app = __revit__.Application

        # Criar novo documento
        log_message("📄 Criando novo documento...")
        doc = app.NewProjectDocument(UnitSystem.Metric)

        # Carregar família
        log_message("📦 Carregando família...")
        with Transaction(doc, "Carregar Família") as trans:
            trans.Start()
            family_loaded = doc.LoadFamily(input_file)
            trans.Commit()

        if not family_loaded:
            raise Exception("Falha ao carregar família")

        # Encontrar símbolo da família
        collector = FilteredElementCollector(doc).OfClass(FamilySymbol)
        family_symbols = list(collector)

        if not family_symbols:
            raise Exception("Nenhum símbolo de família encontrado")

        family_symbol = family_symbols[-1]  # Último carregado

        # Inserir família na origem
        log_message("📍 Inserindo família na origem...")
        with Transaction(doc, "Inserir Família") as trans:
            trans.Start()

            if not family_symbol.IsActive:
                family_symbol.Activate()

            origin = XYZ(0, 0, 0)
            family_instance = doc.Create.NewFamilyInstance(
                origin,
                family_symbol,
                StructuralType.NonStructural
            )

            trans.Commit()

        # Configurar exportação FBX
        log_message("🔄 Configurando exportação FBX...")
        fbx_options = FBXExportOptions()
        fbx_options.ExportRoomsAsFBX = False
        fbx_options.ExportLinkedFiles = False

        # Criar view 3D se necessário
        view_3d = None
        collector = FilteredElementCollector(doc).OfClass(View3D)

        for view in collector:
            if not view.IsTemplate:
                view_3d = view
                break

        if view_3d is None:
            with Transaction(doc, "Criar View 3D") as trans:
                trans.Start()

                view_family_type = None
                collector_vft = FilteredElementCollector(doc).OfClass(ViewFamilyType)

                for vft in collector_vft:
                    if vft.ViewFamily == ViewFamily.ThreeDimensional:
                        view_family_type = vft
                        break

                if view_family_type:
                    view_3d = View3D.CreateIsometric(doc, view_family_type.Id)

                trans.Commit()

        if view_3d is None:
            raise Exception("Não foi possível criar view 3D")

        # Exportar para FBX
        log_message("📤 Exportando para FBX...")
        view_set = ViewSet()
        view_set.Insert(view_3d)

        # Garantir caminho absoluto para saída
        if not os.path.isabs(output_file):
            output_file = os.path.abspath(output_file)

        output_dir = os.path.dirname(output_file)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        result = doc.Export(output_dir, os.path.basename(output_file), view_set, fbx_options)

        if result:
            log_message("✅ Exportação FBX concluída com sucesso!")
        else:
            raise Exception("Falha na exportação FBX")

        # Fechar documento sem salvar
        doc.Close(False)

    except Exception as e:
        log_message(f"❌ Erro: {e}")
        import traceback
        log_message(f"📋 Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
